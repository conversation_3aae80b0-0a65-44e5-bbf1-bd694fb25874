#!/usr/bin/env python3
"""
Enhanced Job Queue Management System
Handles user-specific job queues, automatic fetching, and intelligent caching
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from supabase_client import supabase
from cache import cache, get_cached_jobs, cache_jobs, get_cached_jobs_for_user, add_user_job_exclusion
from job_apis import jooble, map_jooble_job, muse, map_muse_job
from job_service import adzuna, arbeitnow
from api_usage import (
    call_jooble_with_tracking,
    call_adzuna_with_tracking, 
    call_muse_with_tracking,
    call_arbeitnow_with_tracking,
    APILimitReached
)
import httpx

logger = logging.getLogger(__name__)

class QueueStatus(Enum):
    HEALTHY = "healthy"      # >10 jobs
    LOW = "low"             # 5-10 jobs  
    CRITICAL = "critical"   # <5 jobs
    EMPTY = "empty"         # 0 jobs

@dataclass
class UserJobQueue:
    user_id: str
    available_jobs: List[Dict[str, Any]]
    swiped_job_ids: Set[str]
    last_fetched: datetime
    queue_status: QueueStatus
    fetch_in_progress: bool = False
    
class JobQueueManager:
    def __init__(self):
        self.user_queues: Dict[str, UserJobQueue] = {}
        self.min_queue_size = 10
        self.target_queue_size = 80  # Increased to support 50-70 job requests
        self.max_queue_size = 150
        self.fetch_cooldown_minutes = 2  # Reduced cooldown for more responsive fetching

        # Job freshness and rotation settings
        self.job_freshness_hours = 24  # Jobs older than 24 hours are considered stale
        self.rotation_cooldown_hours = 72  # Don't show same job again for 72 hours
        self.source_rotation_enabled = True  # Rotate between different job sources
        
    async def get_user_queue(self, user_id: str) -> UserJobQueue:
        """Get or create user job queue"""
        if user_id not in self.user_queues:
            # Load swiped jobs from database
            swiped_jobs = await self._load_user_swiped_jobs(user_id)
            
            self.user_queues[user_id] = UserJobQueue(
                user_id=user_id,
                available_jobs=[],
                swiped_job_ids=set(swiped_jobs),
                last_fetched=datetime.min,
                queue_status=QueueStatus.EMPTY
            )
            
        return self.user_queues[user_id]
    
    async def _load_user_swiped_jobs(self, user_id: str) -> List[str]:
        """Load user's swiped job IDs from database"""
        try:
            # Get from swipes table
            response = supabase.table("swipes").select("job_id").eq("user_id", user_id).execute()
            swiped_ids = [row["job_id"] for row in response.data] if response.data else []
            
            # Also get from bookmarks
            bookmark_response = supabase.table("user_bookmarks").select("job_id").eq("profile_id", user_id).execute()
            bookmark_ids = [row["job_id"] for row in bookmark_response.data] if bookmark_response.data else []
            
            # Combine and deduplicate
            all_swiped = list(set(swiped_ids + bookmark_ids))
            logger.info(f"Loaded {len(all_swiped)} swiped job IDs for user {user_id}")
            return all_swiped
            
        except Exception as e:
            logger.error(f"Error loading swiped jobs for user {user_id}: {e}")
            return []
    
    def _calculate_queue_status(self, queue_size: int) -> QueueStatus:
        """Calculate queue status based on size"""
        if queue_size == 0:
            return QueueStatus.EMPTY
        elif queue_size < self.min_queue_size:
            return QueueStatus.CRITICAL
        elif queue_size < 30:
            return QueueStatus.LOW
        else:
            return QueueStatus.HEALTHY
    
    async def get_jobs_for_user(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get jobs for user, automatically fetching more if needed"""
        user_queue = await self.get_user_queue(user_id)

        # Filter out swiped jobs from available jobs
        available_jobs = [
            job for job in user_queue.available_jobs
            if job.get("id") not in user_queue.swiped_job_ids
        ]

        # Update queue status
        user_queue.queue_status = self._calculate_queue_status(len(available_jobs))

        logger.info(f"User {user_id} queue: {len(available_jobs)} available, {limit} requested, status: {user_queue.queue_status.value}")

        # Check if we need to fetch more jobs - either queue is low OR we don't have enough for the request
        should_fetch = (
            (user_queue.queue_status in [QueueStatus.CRITICAL, QueueStatus.EMPTY, QueueStatus.LOW] or
             len(available_jobs) < limit) and
            not user_queue.fetch_in_progress
        )

        if should_fetch:
            logger.info(f"Triggering fetch for user {user_id}: queue_status={user_queue.queue_status.value}, available={len(available_jobs)}, requested={limit}")
            # Fetch jobs synchronously to ensure we have enough for this request
            await self._fetch_more_jobs_for_user(user_id)

            # Refresh available jobs after fetch
            user_queue = await self.get_user_queue(user_id)
            available_jobs = [
                job for job in user_queue.available_jobs
                if job.get("id") not in user_queue.swiped_job_ids
            ]
            logger.info(f"After fetch: {len(available_jobs)} jobs available for user {user_id}")

        # Return requested jobs
        return available_jobs[:limit]
    
    async def mark_job_swiped(self, user_id: str, job_id: str):
        """Mark a job as swiped by user"""
        user_queue = await self.get_user_queue(user_id)
        user_queue.swiped_job_ids.add(job_id)

        # Add to cache exclusion list
        await add_user_job_exclusion(user_id, job_id)

        # Remove from available jobs
        user_queue.available_jobs = [
            job for job in user_queue.available_jobs
            if job.get("id") != job_id
        ]

        # Update queue status
        available_count = len([
            job for job in user_queue.available_jobs
            if job.get("id") not in user_queue.swiped_job_ids
        ])
        user_queue.queue_status = self._calculate_queue_status(available_count)

        logger.info(f"Marked job {job_id} as swiped for user {user_id}, queue size: {available_count}")

    async def _fetch_more_jobs_for_user(self, user_id: str):
        """Fetch more jobs for a user when queue is low"""
        user_queue = await self.get_user_queue(user_id)

        # Check cooldown
        if (datetime.now() - user_queue.last_fetched).total_seconds() < self.fetch_cooldown_minutes * 60:
            logger.info(f"Fetch cooldown active for user {user_id}")
            return

        user_queue.fetch_in_progress = True

        try:
            # Get user preferences
            user_prefs = await self._get_user_preferences(user_id)
            if not user_prefs:
                logger.warning(f"No preferences found for user {user_id}")
                return

            # Check cache first
            cached_jobs = await self._get_cached_jobs_for_user(user_prefs)

            if cached_jobs:
                logger.info(f"Found {len(cached_jobs)} cached jobs for user {user_id}")
                new_jobs = self._filter_new_jobs(cached_jobs, user_queue.swiped_job_ids)
                user_queue.available_jobs.extend(new_jobs)
            else:
                # Fetch fresh jobs from APIs
                logger.info(f"No cache hit, fetching fresh jobs for user {user_id}")
                fresh_jobs = await self._fetch_fresh_jobs(user_prefs)

                if fresh_jobs:
                    # Cache for other users
                    await self._cache_jobs_for_preferences(user_prefs, fresh_jobs)

                    # Add to user queue
                    new_jobs = self._filter_new_jobs(fresh_jobs, user_queue.swiped_job_ids)
                    user_queue.available_jobs.extend(new_jobs)

                    logger.info(f"Fetched {len(fresh_jobs)} fresh jobs, {len(new_jobs)} new for user {user_id}")

            # Limit queue size
            if len(user_queue.available_jobs) > self.max_queue_size:
                user_queue.available_jobs = user_queue.available_jobs[:self.max_queue_size]

            user_queue.last_fetched = datetime.now()
            user_queue.queue_status = self._calculate_queue_status(len(user_queue.available_jobs))

        except Exception as e:
            logger.error(f"Error fetching jobs for user {user_id}: {e}")
        finally:
            user_queue.fetch_in_progress = False

    async def _get_user_preferences(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user job preferences"""
        try:
            response = supabase.table("user_job_preferences").select("*").eq("user_id", user_id).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"Error getting preferences for user {user_id}: {e}")
            return None

    def _filter_new_jobs(self, jobs: List[Dict[str, Any]], swiped_ids: Set[str]) -> List[Dict[str, Any]]:
        """Filter out already swiped jobs"""
        return [job for job in jobs if job.get("id") not in swiped_ids]

    async def _get_cached_jobs_for_user(self, user_prefs: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Get cached jobs matching user preferences, filtered for user"""
        try:
            locations = user_prefs.get("preferred_locations", ["Remote"])
            job_types = user_prefs.get("preferred_job_types", ["Full-time"])
            experience = user_prefs.get("experience_level", "mid")
            industries = user_prefs.get("preferred_industries", [])
            user_id = user_prefs.get("user_id")

            # Try to get from cache with user filtering
            for location in locations[:2]:  # Try top 2 locations
                if user_id:
                    cached_data = await get_cached_jobs_for_user(
                        location, job_types[:2], experience, industries[:2], user_id
                    )
                else:
                    cached_data = await get_cached_jobs(location, job_types[:2], experience, industries[:2])

                if cached_data and cached_data.get("jobs"):
                    return cached_data["jobs"]

            return None
        except Exception as e:
            logger.error(f"Error getting cached jobs: {e}")
            return None

    async def _fetch_fresh_jobs(self, user_prefs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch fresh jobs from multiple APIs"""
        all_jobs = []

        locations = user_prefs.get("preferred_locations", ["Remote"])
        job_types = user_prefs.get("preferred_job_types", ["Full-time"])
        industries = user_prefs.get("preferred_industries", [])

        # Determine which APIs to call based on location
        is_us_location = any(
            loc for loc in locations
            if any(us_indicator in loc.lower() for us_indicator in ['usa', 'us', 'united states', 'california', 'new york', 'texas'])
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            tasks = []

            # Always call Jooble (global coverage)
            for location in locations[:2]:
                for job_type in job_types[:2]:
                    query = f"{job_type} {' '.join(industries[:2])}" if industries else job_type
                    tasks.append(self._call_jooble_safe(client, query, location))

            # Call additional APIs based on location
            if is_us_location:
                # US-specific APIs
                for location in locations[:1]:
                    tasks.append(self._call_adzuna_safe(client, job_types[0] if job_types else "software", location))

                    if industries and any(tech in ' '.join(industries).lower() for tech in ['technology', 'software', 'computer']):
                        tasks.append(self._call_muse_safe(client, "Computer and IT", location))

            # International APIs
            for location in locations[:1]:
                tasks.append(self._call_arbeitnow_safe(client, job_types[0] if job_types else "software"))

            # Execute all API calls concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in results:
                if isinstance(result, list) and result:
                    all_jobs.extend(result)
                elif isinstance(result, Exception):
                    logger.warning(f"API call failed: {result}")

        # Remove duplicates and limit
        unique_jobs = self._remove_duplicates(all_jobs)
        logger.info(f"Fetched {len(all_jobs)} total jobs, {len(unique_jobs)} unique")

        # Apply freshness filtering and rotation
        fresh_jobs = self._apply_freshness_rotation(unique_jobs, user_prefs.get("user_id"))

        return fresh_jobs[:self.target_queue_size]

    async def _call_jooble_safe(self, client: httpx.AsyncClient, query: str, location: str) -> List[Dict[str, Any]]:
        """Safely call Jooble API with error handling"""
        try:
            result = await call_jooble_with_tracking(client, query, location)
            if result and isinstance(result, list):
                return [map_jooble_job(job) for job in result[:25]]
            return []
        except APILimitReached:
            logger.warning("Jooble API limit reached")
            return []
        except Exception as e:
            logger.error(f"Jooble API error: {e}")
            return []

    async def _call_adzuna_safe(self, client: httpx.AsyncClient, query: str, location: str) -> List[Dict[str, Any]]:
        """Safely call Adzuna API with error handling"""
        try:
            result = await call_adzuna_with_tracking(client, query, location)
            if result and isinstance(result, list):
                return result[:25]
            return []
        except APILimitReached:
            logger.warning("Adzuna API limit reached")
            return []
        except Exception as e:
            logger.error(f"Adzuna API error: {e}")
            return []

    async def _call_muse_safe(self, client: httpx.AsyncClient, category: str, location: str) -> List[Dict[str, Any]]:
        """Safely call Muse API with error handling"""
        try:
            result = await call_muse_with_tracking(client, category, location)
            if result and isinstance(result, list):
                return [map_muse_job(job) for job in result[:25]]
            return []
        except APILimitReached:
            logger.warning("Muse API limit reached")
            return []
        except Exception as e:
            logger.error(f"Muse API error: {e}")
            return []

    async def _call_arbeitnow_safe(self, client: httpx.AsyncClient, query: str) -> List[Dict[str, Any]]:
        """Safely call Arbeitnow API with error handling"""
        try:
            result = await call_arbeitnow_with_tracking(client, query)
            if result and isinstance(result, list):
                return result[:25]
            return []
        except APILimitReached:
            logger.warning("Arbeitnow API limit reached")
            return []
        except Exception as e:
            logger.error(f"Arbeitnow API error: {e}")
            return []

    def _remove_duplicates(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate jobs based on title and company"""
        seen = set()
        unique_jobs = []

        for job in jobs:
            # Create a key based on title and company
            title = job.get("title", "").lower().strip()
            company = job.get("company", "").lower().strip()
            key = f"{title}|{company}"

            if key not in seen and title and company:
                seen.add(key)
                unique_jobs.append(job)

        return unique_jobs

    async def _cache_jobs_for_preferences(self, user_prefs: Dict[str, Any], jobs: List[Dict[str, Any]]):
        """Cache jobs for user preferences to benefit other users"""
        try:
            locations = user_prefs.get("preferred_locations", ["Remote"])
            job_types = user_prefs.get("preferred_job_types", ["Full-time"])
            experience = user_prefs.get("experience_level", "mid")
            industries = user_prefs.get("preferred_industries", [])

            # Cache for the primary location/job type combination
            if locations and job_types:
                cache_data = {
                    "jobs": jobs,
                    "fetched_at": datetime.now().isoformat(),
                    "total_jobs": len(jobs)
                }

                await cache_jobs(
                    location=locations[0],
                    job_types=job_types[:2],
                    experience=experience,
                    industries=industries[:2],
                    jobs_data=cache_data,
                    user_count=1
                )

                logger.info(f"Cached {len(jobs)} jobs for preferences: {locations[0]}, {job_types[:2]}")

        except Exception as e:
            logger.error(f"Error caching jobs: {e}")

    async def reset_user_queue(self, user_id: str):
        """Reset user's job queue and swiped history (for testing or user request)"""
        try:
            user_queue = await self.get_user_queue(user_id)

            # Clear swiped jobs (but keep in database for learning)
            user_queue.swiped_job_ids.clear()
            user_queue.available_jobs.clear()
            user_queue.last_fetched = datetime.min
            user_queue.queue_status = QueueStatus.EMPTY

            logger.info(f"Reset job queue for user {user_id}")

            # Trigger fresh fetch
            await self._fetch_more_jobs_for_user(user_id)

        except Exception as e:
            logger.error(f"Error resetting queue for user {user_id}: {e}")

    async def get_queue_stats(self, user_id: str) -> Dict[str, Any]:
        """Get queue statistics for monitoring"""
        user_queue = await self.get_user_queue(user_id)

        available_count = len([
            job for job in user_queue.available_jobs
            if job.get("id") not in user_queue.swiped_job_ids
        ])

        return {
            "user_id": user_id,
            "queue_status": user_queue.queue_status.value,
            "available_jobs": available_count,
            "total_jobs_in_queue": len(user_queue.available_jobs),
            "swiped_jobs_count": len(user_queue.swiped_job_ids),
            "last_fetched": user_queue.last_fetched.isoformat() if user_queue.last_fetched != datetime.min else None,
            "fetch_in_progress": user_queue.fetch_in_progress
        }

    def _apply_freshness_rotation(self, jobs: List[Dict[str, Any]], user_id: str) -> List[Dict[str, Any]]:
        """Apply job freshness filtering and source rotation"""
        if not jobs:
            return jobs

        current_time = datetime.now()
        fresh_jobs = []

        for job in jobs:
            # Check job freshness
            job_posted_date = self._parse_job_date(job.get("posted_date"))
            if job_posted_date:
                hours_since_posted = (current_time - job_posted_date).total_seconds() / 3600

                # Skip very old jobs unless queue is critical
                if hours_since_posted > self.job_freshness_hours * 7:  # 7 days max
                    continue

                # Add freshness score
                job["freshness_score"] = max(0, 1 - (hours_since_posted / (self.job_freshness_hours * 7)))
            else:
                # No date available, assume moderate freshness
                job["freshness_score"] = 0.5

            # Add source diversity score
            job["source_score"] = self._calculate_source_score(job.get("source", "unknown"))

            fresh_jobs.append(job)

        # Sort by combined score (freshness + source diversity + quality)
        fresh_jobs.sort(key=lambda x: (
            x.get("freshness_score", 0) * 0.3 +
            x.get("source_score", 0) * 0.2 +
            x.get("quality_score", 0.5) * 0.5
        ), reverse=True)

        return fresh_jobs

    def _parse_job_date(self, date_str: str) -> Optional[datetime]:
        """Parse job posting date from various formats"""
        if not date_str:
            return None

        try:
            # Try common date formats
            formats = [
                "%Y-%m-%d",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%d %H:%M:%S",
                "%m/%d/%Y",
                "%d/%m/%Y"
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            # Handle relative dates like "2 days ago"
            if "ago" in date_str.lower():
                return self._parse_relative_date(date_str)

            return None

        except Exception as e:
            logger.warning(f"Error parsing job date '{date_str}': {e}")
            return None

    def _parse_relative_date(self, date_str: str) -> Optional[datetime]:
        """Parse relative dates like '2 days ago', '1 week ago'"""
        try:
            import re

            # Extract number and unit
            match = re.search(r'(\d+)\s*(day|week|month|hour)s?\s*ago', date_str.lower())
            if not match:
                return None

            number = int(match.group(1))
            unit = match.group(2)

            current_time = datetime.now()

            if unit == "hour":
                return current_time - timedelta(hours=number)
            elif unit == "day":
                return current_time - timedelta(days=number)
            elif unit == "week":
                return current_time - timedelta(weeks=number)
            elif unit == "month":
                return current_time - timedelta(days=number * 30)  # Approximate

            return None

        except Exception as e:
            logger.warning(f"Error parsing relative date '{date_str}': {e}")
            return None

    def _calculate_source_score(self, source: str) -> float:
        """Calculate source diversity score to promote variety"""
        # Promote less common sources for diversity
        source_weights = {
            "jooble": 0.7,      # Common, lower weight
            "adzuna": 0.8,      # Moderate
            "muse": 0.9,        # Less common, higher weight
            "arbeitnow": 0.9,   # Less common, higher weight
            "remoteok": 0.6,    # Very common, lowest weight
            "unknown": 0.5      # Default
        }

        return source_weights.get(source.lower(), 0.5)

# Global instance
job_queue_manager = JobQueueManager()
